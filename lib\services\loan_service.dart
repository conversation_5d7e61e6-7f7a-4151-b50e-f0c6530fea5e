/// خدمة إدارة القروض والسلف
/// توفر عمليات إدارة القروض والأقساط للموظفين
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';

/// نموذج القرض
class Loan {
  final int? id;
  final int employeeId;
  final double amount;
  final double interestRate;
  final int installments;
  final double monthlyInstallment;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final DateTime loanDate;
  final DateTime? firstInstallmentDate;
  final String status;
  final String? purpose;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Loan({
    this.id,
    required this.employeeId,
    required this.amount,
    this.interestRate = 0,
    required this.installments,
    required this.monthlyInstallment,
    required this.totalAmount,
    this.paidAmount = 0,
    required this.remainingAmount,
    required this.loanDate,
    this.firstInstallmentDate,
    this.status = 'active',
    this.purpose,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Loan.fromMap(Map<String, dynamic> map) {
    return Loan(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      amount: (map['amount'] as num).toDouble(),
      interestRate: (map['interest_rate'] as num).toDouble(),
      installments: map['installments'] as int,
      monthlyInstallment: (map['monthly_installment'] as num).toDouble(),
      totalAmount: (map['total_amount'] as num).toDouble(),
      paidAmount: (map['paid_amount'] as num).toDouble(),
      remainingAmount: (map['remaining_amount'] as num).toDouble(),
      loanDate: DateTime.parse(map['loan_date'] as String),
      firstInstallmentDate: map['first_installment_date'] != null
          ? DateTime.parse(map['first_installment_date'] as String)
          : null,
      status: map['status'] as String,
      purpose: map['purpose'] as String?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'amount': amount,
      'interest_rate': interestRate,
      'installments': installments,
      'monthly_installment': monthlyInstallment,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'loan_date': loanDate.toIso8601String().split('T')[0],
      'first_installment_date': firstInstallmentDate?.toIso8601String().split(
        'T',
      )[0],
      'status': status,
      'purpose': purpose,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة من القرض
  Loan copyWith({
    int? id,
    int? employeeId,
    double? amount,
    double? interestRate,
    int? installments,
    double? monthlyInstallment,
    double? totalAmount,
    double? paidAmount,
    double? remainingAmount,
    DateTime? loanDate,
    DateTime? firstInstallmentDate,
    String? status,
    String? purpose,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Loan(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      amount: amount ?? this.amount,
      interestRate: interestRate ?? this.interestRate,
      installments: installments ?? this.installments,
      monthlyInstallment: monthlyInstallment ?? this.monthlyInstallment,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      loanDate: loanDate ?? this.loanDate,
      firstInstallmentDate: firstInstallmentDate ?? this.firstInstallmentDate,
      status: status ?? this.status,
      purpose: purpose ?? this.purpose,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من نشاط القرض
  bool get isActive => status == 'active';

  /// التحقق من اكتمال سداد القرض
  bool get isCompleted => status == 'completed' || remainingAmount <= 0;

  /// التحقق من إلغاء القرض
  bool get isCancelled => status == 'cancelled';

  /// حساب نسبة السداد
  double get paymentProgress =>
      totalAmount > 0 ? (paidAmount / totalAmount) : 0;

  /// عدد الأقساط المتبقية
  int get remainingInstallments => monthlyInstallment > 0
      ? (remainingAmount / monthlyInstallment).ceil()
      : 0;
}

/// نموذج قسط القرض
class LoanInstallment {
  final int? id;
  final int loanId;
  final int installmentNumber;
  final double amount;
  final DateTime dueDate;
  final DateTime? paidDate;
  final double paidAmount;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LoanInstallment({
    this.id,
    required this.loanId,
    required this.installmentNumber,
    required this.amount,
    required this.dueDate,
    this.paidDate,
    this.paidAmount = 0,
    this.status = 'pending',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LoanInstallment.fromMap(Map<String, dynamic> map) {
    return LoanInstallment(
      id: map['id'] as int?,
      loanId: map['loan_id'] as int,
      installmentNumber: map['installment_number'] as int,
      amount: (map['amount'] as num).toDouble(),
      dueDate: DateTime.parse(map['due_date'] as String),
      paidDate: map['paid_date'] != null
          ? DateTime.parse(map['paid_date'] as String)
          : null,
      paidAmount: (map['paid_amount'] as num).toDouble(),
      status: map['status'] as String,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'loan_id': loanId,
      'installment_number': installmentNumber,
      'amount': amount,
      'due_date': dueDate.toIso8601String().split('T')[0],
      'paid_date': paidDate?.toIso8601String().split('T')[0],
      'paid_amount': paidAmount,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// التحقق من سداد القسط
  bool get isPaid => status == 'paid';

  /// التحقق من تأخر القسط
  bool get isOverdue => status == 'pending' && dueDate.isBefore(DateTime.now());

  /// التحقق من استحقاق القسط قريباً (خلال 7 أيام)
  bool get isDueSoon =>
      status == 'pending' &&
      dueDate.isAfter(DateTime.now()) &&
      dueDate.isBefore(DateTime.now().add(const Duration(days: 7)));
}

class LoanService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء قرض جديد
  Future<Loan> createLoan({
    required int employeeId,
    required double amount,
    double interestRate = 0,
    required int installments,
    DateTime? loanDate,
    DateTime? firstInstallmentDate,
    String? purpose,
    String? notes,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();
      final actualLoanDate = loanDate ?? now;
      final actualFirstInstallmentDate =
          firstInstallmentDate ??
          DateTime(
            actualLoanDate.year,
            actualLoanDate.month + 1,
            actualLoanDate.day,
          );

      // حساب المبلغ الإجمالي مع الفوائد
      final totalAmount = amount + (amount * interestRate / 100);

      // حساب القسط الشهري
      final monthlyInstallment = totalAmount / installments;

      final loan = Loan(
        employeeId: employeeId,
        amount: amount,
        interestRate: interestRate,
        installments: installments,
        monthlyInstallment: monthlyInstallment,
        totalAmount: totalAmount,
        remainingAmount: totalAmount,
        loanDate: actualLoanDate,
        firstInstallmentDate: actualFirstInstallmentDate,
        purpose: purpose,
        notes: notes,
        createdAt: now,
        updatedAt: now,
      );

      // حفظ القرض
      final loanId = await db.insert(AppConstants.loansTable, loan.toMap());

      final savedLoan = loan.copyWith(id: loanId);

      // إنشاء جدول الأقساط
      await _generateInstallments(savedLoan);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'Loan',
        entityId: loanId,
        description: 'إنشاء قرض جديد للموظف',
        newValues: savedLoan.toMap(),
      );

      LoggingService.info(
        'تم إنشاء قرض جديد بنجاح',
        category: 'LoanService',
        data: {
          'employeeId': employeeId,
          'amount': amount,
          'installments': installments,
        },
      );

      return savedLoan;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء القرض',
        category: 'LoanService',
        data: {
          'employeeId': employeeId,
          'amount': amount,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// إنشاء جدول الأقساط للقرض
  Future<void> _generateInstallments(Loan loan) async {
    try {
      final db = await _databaseHelper.database;
      final installments = <LoanInstallment>[];

      for (int i = 1; i <= loan.installments; i++) {
        final dueDate = DateTime(
          loan.firstInstallmentDate!.year,
          loan.firstInstallmentDate!.month + (i - 1),
          loan.firstInstallmentDate!.day,
        );

        final installment = LoanInstallment(
          loanId: loan.id!,
          installmentNumber: i,
          amount: loan.monthlyInstallment,
          dueDate: dueDate,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        installments.add(installment);
      }

      // حفظ جميع الأقساط
      for (final installment in installments) {
        await db.insert(
          AppConstants.loanInstallmentsTable,
          installment.toMap(),
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء جدول الأقساط',
        category: 'LoanService',
        data: {'loanId': loan.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع قروض موظف
  Future<List<Loan>> getEmployeeLoans(int employeeId) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.loansTable,
        where: 'employee_id = ?',
        whereArgs: [employeeId],
        orderBy: 'loan_date DESC',
      );

      return result.map((map) => Loan.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على قروض الموظف',
        category: 'LoanService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على القروض النشطة لموظف
  Future<List<Loan>> getActiveEmployeeLoans(int employeeId) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.loansTable,
        where: 'employee_id = ? AND status = ?',
        whereArgs: [employeeId, 'active'],
        orderBy: 'loan_date DESC',
      );

      return result.map((map) => Loan.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على القروض النشطة',
        category: 'LoanService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على قرض بالمعرف
  Future<Loan?> getLoanById(int loanId) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.loansTable,
        where: 'id = ?',
        whereArgs: [loanId],
      );

      if (result.isNotEmpty) {
        return Loan.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على القرض',
        category: 'LoanService',
        data: {'loanId': loanId, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على أقساط قرض
  Future<List<LoanInstallment>> getLoanInstallments(int loanId) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.loanInstallmentsTable,
        where: 'loan_id = ?',
        whereArgs: [loanId],
        orderBy: 'installment_number ASC',
      );

      return result.map((map) => LoanInstallment.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على أقساط القرض',
        category: 'LoanService',
        data: {'loanId': loanId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// دفع قسط قرض
  Future<void> payInstallment({
    required int installmentId,
    double? paidAmount,
    DateTime? paidDate,
    String? notes,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final now = DateTime.now();
      final actualPaidDate = paidDate ?? now;

      // الحصول على بيانات القسط
      final installmentResult = await db.query(
        AppConstants.loanInstallmentsTable,
        where: 'id = ?',
        whereArgs: [installmentId],
        limit: 1,
      );

      if (installmentResult.isEmpty) {
        throw ValidationException('القسط غير موجود');
      }

      final installment = LoanInstallment.fromMap(installmentResult.first);

      if (installment.isPaid) {
        throw ValidationException('تم دفع هذا القسط مسبقاً');
      }

      final actualPaidAmount = paidAmount ?? installment.amount;

      // تحديث القسط
      await db.update(
        AppConstants.loanInstallmentsTable,
        {
          'paid_date': actualPaidDate.toIso8601String().split('T')[0],
          'paid_amount': actualPaidAmount,
          'status': 'paid',
          'notes': notes,
          'updated_at': now.toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [installmentId],
      );

      // تحديث بيانات القرض
      await _updateLoanProgress(installment.loanId);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'PAY_INSTALLMENT',
        entityType: 'LoanInstallment',
        entityId: installmentId,
        description: 'دفع قسط قرض',
        oldValues: installment.toMap(),
        newValues: {
          'paid_amount': actualPaidAmount,
          'paid_date': actualPaidDate.toIso8601String(),
          'status': 'paid',
        },
      );

      LoggingService.info(
        'تم دفع قسط القرض بنجاح',
        category: 'LoanService',
        data: {
          'installmentId': installmentId,
          'loanId': installment.loanId,
          'paidAmount': actualPaidAmount,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في دفع قسط القرض',
        category: 'LoanService',
        data: {'installmentId': installmentId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث تقدم سداد القرض
  Future<void> _updateLoanProgress(int loanId) async {
    try {
      final db = await _databaseHelper.database;

      // حساب المبلغ المدفوع
      final paidResult = await db.rawQuery(
        'SELECT SUM(paid_amount) as total_paid FROM ${AppConstants.loanInstallmentsTable} WHERE loan_id = ? AND status = ?',
        [loanId, 'paid'],
      );

      final totalPaid =
          (paidResult.first['total_paid'] as num?)?.toDouble() ?? 0;

      // الحصول على بيانات القرض
      final loanResult = await db.query(
        AppConstants.loansTable,
        where: 'id = ?',
        whereArgs: [loanId],
        limit: 1,
      );

      if (loanResult.isNotEmpty) {
        final loan = Loan.fromMap(loanResult.first);
        final remainingAmount = loan.totalAmount - totalPaid;
        final newStatus = remainingAmount <= 0 ? 'completed' : 'active';

        // تحديث القرض
        await db.update(
          AppConstants.loansTable,
          {
            'paid_amount': totalPaid,
            'remaining_amount': remainingAmount,
            'status': newStatus,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [loanId],
        );
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث تقدم القرض',
        category: 'LoanService',
        data: {'loanId': loanId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حساب إجمالي خصم القروض لموظف في شهر معين
  Future<double> calculateMonthlyLoanDeduction(
    int employeeId,
    int month,
    int year,
  ) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على الأقساط المستحقة في الشهر المحدد
      final result = await db.rawQuery(
        '''
        SELECT SUM(li.amount) as total_deduction
        FROM ${AppConstants.loanInstallmentsTable} li
        INNER JOIN ${AppConstants.loansTable} l ON li.loan_id = l.id
        WHERE l.employee_id = ?
        AND li.status = 'pending'
        AND strftime('%Y', li.due_date) = ?
        AND strftime('%m', li.due_date) = ?
      ''',
        [employeeId, year.toString(), month.toString().padLeft(2, '0')],
      );

      return (result.first['total_deduction'] as num?)?.toDouble() ?? 0;
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب خصم القروض الشهري',
        category: 'LoanService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// الحصول على الأقساط المتأخرة
  Future<List<LoanInstallment>> getOverdueInstallments() async {
    try {
      final db = await _databaseHelper.database;
      final today = DateTime.now().toIso8601String().split('T')[0];

      final result = await db.query(
        AppConstants.loanInstallmentsTable,
        where: 'status = ? AND due_date < ?',
        whereArgs: ['pending', today],
        orderBy: 'due_date ASC',
      );

      return result.map((map) => LoanInstallment.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على الأقساط المتأخرة',
        category: 'LoanService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }
}
