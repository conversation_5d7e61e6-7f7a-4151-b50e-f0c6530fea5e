/// نماذج نظام الموارد البشرية
library;

import '../constants/app_constants.dart';

/// نموذج القسم
class Department {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final int? managerId;
  final int? costCenterAccountId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Department({
    this.id,
    required this.code,
    required this.name,
    this.description,
    this.managerId,
    this.costCenterAccountId,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Department.fromMap(Map<String, dynamic> map) {
    return Department(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      managerId: map['manager_id'] as int?,
      costCenterAccountId: map['cost_center_account_id'] as int?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'code': code,
      'name': name,
      'description': description,
      'manager_id': managerId,
      'cost_center_account_id': costCenterAccountId,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Department copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    int? managerId,
    int? costCenterAccountId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Department(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      managerId: managerId ?? this.managerId,
      costCenterAccountId: costCenterAccountId ?? this.costCenterAccountId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج المنصب
class Position {
  final int? id;
  final String code;
  final String title;
  final String? description;
  final int? departmentId;
  final double minSalary;
  final double maxSalary;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Position({
    this.id,
    required this.code,
    required this.title,
    this.description,
    this.departmentId,
    this.minSalary = 0,
    this.maxSalary = 0,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Position.fromMap(Map<String, dynamic> map) {
    return Position(
      id: map['id'] as int?,
      code: map['code'] as String,
      title: map['title'] as String,
      description: map['description'] as String?,
      departmentId: map['department_id'] as int?,
      minSalary: (map['min_salary'] as num).toDouble(),
      maxSalary: (map['max_salary'] as num).toDouble(),
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'code': code,
      'title': title,
      'description': description,
      'department_id': departmentId,
      'min_salary': minSalary,
      'max_salary': maxSalary,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Position copyWith({
    int? id,
    String? code,
    String? title,
    String? description,
    int? departmentId,
    double? minSalary,
    double? maxSalary,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Position(
      id: id ?? this.id,
      code: code ?? this.code,
      title: title ?? this.title,
      description: description ?? this.description,
      departmentId: departmentId ?? this.departmentId,
      minSalary: minSalary ?? this.minSalary,
      maxSalary: maxSalary ?? this.maxSalary,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج الموظف
class Employee {
  final int? id;
  final String employeeNumber;
  final String nationalId;
  final String firstName;
  final String lastName;
  final String fullName;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? maritalStatus;
  final String? phone;
  final String? email;
  final String? address;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final int? departmentId;
  final int? positionId;
  final DateTime hireDate;
  final DateTime? terminationDate;
  final String status;
  final double basicSalary;
  final int? costCenterAccountId;
  final String? bankAccountNumber;
  final String? bankName;
  final String? socialInsuranceNumber;
  final String? taxNumber;
  final String? photoPath;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Employee({
    this.id,
    required this.employeeNumber,
    required this.nationalId,
    required this.firstName,
    required this.lastName,
    required this.fullName,
    this.dateOfBirth,
    this.gender,
    this.maritalStatus,
    this.phone,
    this.email,
    this.address,
    this.emergencyContactName,
    this.emergencyContactPhone,
    this.departmentId,
    this.positionId,
    required this.hireDate,
    this.terminationDate,
    this.status = AppConstants.employeeStatusActive,
    this.basicSalary = 0,
    this.costCenterAccountId,
    this.bankAccountNumber,
    this.bankName,
    this.socialInsuranceNumber,
    this.taxNumber,
    this.photoPath,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Employee.fromMap(Map<String, dynamic> map) {
    return Employee(
      id: map['id'] as int?,
      employeeNumber: map['employee_number'] as String,
      nationalId: map['national_id'] as String,
      firstName: map['first_name'] as String,
      lastName: map['last_name'] as String,
      fullName: map['full_name'] as String,
      dateOfBirth: map['date_of_birth'] != null
          ? DateTime.parse(map['date_of_birth'] as String)
          : null,
      gender: map['gender'] as String?,
      maritalStatus: map['marital_status'] as String?,
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      address: map['address'] as String?,
      emergencyContactName: map['emergency_contact_name'] as String?,
      emergencyContactPhone: map['emergency_contact_phone'] as String?,
      departmentId: map['department_id'] as int?,
      positionId: map['position_id'] as int?,
      hireDate: DateTime.parse(map['hire_date'] as String),
      terminationDate: map['termination_date'] != null
          ? DateTime.parse(map['termination_date'] as String)
          : null,
      status: map['status'] as String,
      basicSalary: (map['basic_salary'] as num).toDouble(),
      costCenterAccountId: map['cost_center_account_id'] as int?,
      bankAccountNumber: map['bank_account_number'] as String?,
      bankName: map['bank_name'] as String?,
      socialInsuranceNumber: map['social_insurance_number'] as String?,
      taxNumber: map['tax_number'] as String?,
      photoPath: map['photo_path'] as String?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_number': employeeNumber,
      'national_id': nationalId,
      'first_name': firstName,
      'last_name': lastName,
      'full_name': fullName,
      'date_of_birth': dateOfBirth?.toIso8601String().split('T')[0],
      'gender': gender,
      'marital_status': maritalStatus,
      'phone': phone,
      'email': email,
      'address': address,
      'emergency_contact_name': emergencyContactName,
      'emergency_contact_phone': emergencyContactPhone,
      'department_id': departmentId,
      'position_id': positionId,
      'hire_date': hireDate.toIso8601String().split('T')[0],
      'termination_date': terminationDate?.toIso8601String().split('T')[0],
      'status': status,
      'basic_salary': basicSalary,
      'cost_center_account_id': costCenterAccountId,
      'bank_account_number': bankAccountNumber,
      'bank_name': bankName,
      'social_insurance_number': socialInsuranceNumber,
      'tax_number': taxNumber,
      'photo_path': photoPath,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Employee copyWith({
    int? id,
    String? employeeNumber,
    String? nationalId,
    String? firstName,
    String? lastName,
    String? fullName,
    DateTime? dateOfBirth,
    String? gender,
    String? maritalStatus,
    String? phone,
    String? email,
    String? address,
    String? emergencyContactName,
    String? emergencyContactPhone,
    int? departmentId,
    int? positionId,
    DateTime? hireDate,
    DateTime? terminationDate,
    String? status,
    double? basicSalary,
    int? costCenterAccountId,
    String? bankAccountNumber,
    String? bankName,
    String? socialInsuranceNumber,
    String? taxNumber,
    String? photoPath,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      employeeNumber: employeeNumber ?? this.employeeNumber,
      nationalId: nationalId ?? this.nationalId,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      fullName: fullName ?? this.fullName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      emergencyContactPhone:
          emergencyContactPhone ?? this.emergencyContactPhone,
      departmentId: departmentId ?? this.departmentId,
      positionId: positionId ?? this.positionId,
      hireDate: hireDate ?? this.hireDate,
      terminationDate: terminationDate ?? this.terminationDate,
      status: status ?? this.status,
      basicSalary: basicSalary ?? this.basicSalary,
      costCenterAccountId: costCenterAccountId ?? this.costCenterAccountId,
      bankAccountNumber: bankAccountNumber ?? this.bankAccountNumber,
      bankName: bankName ?? this.bankName,
      socialInsuranceNumber:
          socialInsuranceNumber ?? this.socialInsuranceNumber,
      taxNumber: taxNumber ?? this.taxNumber,
      photoPath: photoPath ?? this.photoPath,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// الحصول على الاسم الكامل
  String get displayName =>
      fullName.isNotEmpty ? fullName : '$firstName $lastName';
}

/// نموذج العقد
class EmployeeContract {
  final int? id;
  final int employeeId;
  final String contractType;
  final DateTime startDate;
  final DateTime? endDate;
  final double salary;
  final String workingHours;
  final int vacationDays;
  final String? benefits;
  final String? terms;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EmployeeContract({
    this.id,
    required this.employeeId,
    required this.contractType,
    required this.startDate,
    this.endDate,
    required this.salary,
    required this.workingHours,
    this.vacationDays = 21,
    this.benefits,
    this.terms,
    this.status = 'active',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeContract.fromMap(Map<String, dynamic> map) {
    return EmployeeContract(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      contractType: map['contract_type'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      salary: (map['salary'] as num).toDouble(),
      workingHours: map['working_hours'] as String,
      vacationDays: map['vacation_days'] as int,
      benefits: map['benefits'] as String?,
      terms: map['terms'] as String?,
      status: map['status'] as String,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'contract_type': contractType,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'salary': salary,
      'working_hours': workingHours,
      'vacation_days': vacationDays,
      'benefits': benefits,
      'terms': terms,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة من العقد
  EmployeeContract copyWith({
    int? id,
    int? employeeId,
    String? contractType,
    DateTime? startDate,
    DateTime? endDate,
    double? salary,
    String? workingHours,
    int? vacationDays,
    String? benefits,
    String? terms,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeContract(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      contractType: contractType ?? this.contractType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      salary: salary ?? this.salary,
      workingHours: workingHours ?? this.workingHours,
      vacationDays: vacationDays ?? this.vacationDays,
      benefits: benefits ?? this.benefits,
      terms: terms ?? this.terms,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من نشاط العقد
  bool get isActive => status == 'active';

  /// التحقق من انتهاء العقد
  bool get isExpired => endDate != null && endDate!.isBefore(DateTime.now());

  /// التحقق من قرب انتهاء العقد (خلال 30 يوم)
  bool get isExpiringSoon =>
      endDate != null &&
      endDate!.isAfter(DateTime.now()) &&
      endDate!.isBefore(DateTime.now().add(const Duration(days: 30)));

  /// حساب مدة العقد بالأيام
  int get contractDurationDays {
    if (endDate == null) return -1; // عقد مفتوح
    return endDate!.difference(startDate).inDays;
  }

  /// حساب الأيام المتبقية في العقد
  int get remainingDays {
    if (endDate == null) return -1; // عقد مفتوح
    final remaining = endDate!.difference(DateTime.now()).inDays;
    return remaining > 0 ? remaining : 0;
  }
}

/// نموذج الحضور
class Attendance {
  final int? id;
  final int employeeId;
  final DateTime attendanceDate;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;
  final DateTime? breakStartTime;
  final DateTime? breakEndTime;
  final double totalHours;
  final double regularHours;
  final double overtimeHours;
  final int lateMinutes;
  final int earlyLeaveMinutes;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Attendance({
    this.id,
    required this.employeeId,
    required this.attendanceDate,
    this.checkInTime,
    this.checkOutTime,
    this.breakStartTime,
    this.breakEndTime,
    this.totalHours = 0,
    this.regularHours = 0,
    this.overtimeHours = 0,
    this.lateMinutes = 0,
    this.earlyLeaveMinutes = 0,
    this.status = 'present',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Attendance.fromMap(Map<String, dynamic> map) {
    return Attendance(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      attendanceDate: DateTime.parse(map['attendance_date'] as String),
      checkInTime: map['check_in_time'] != null
          ? DateTime.parse(map['check_in_time'] as String)
          : null,
      checkOutTime: map['check_out_time'] != null
          ? DateTime.parse(map['check_out_time'] as String)
          : null,
      breakStartTime: map['break_start_time'] != null
          ? DateTime.parse(map['break_start_time'] as String)
          : null,
      breakEndTime: map['break_end_time'] != null
          ? DateTime.parse(map['break_end_time'] as String)
          : null,
      totalHours: (map['total_hours'] as num).toDouble(),
      regularHours: (map['regular_hours'] as num).toDouble(),
      overtimeHours: (map['overtime_hours'] as num).toDouble(),
      lateMinutes: map['late_minutes'] as int,
      earlyLeaveMinutes: map['early_leave_minutes'] as int,
      status: map['status'] as String,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'attendance_date': attendanceDate.toIso8601String().split('T')[0],
      'check_in_time': checkInTime?.toIso8601String(),
      'check_out_time': checkOutTime?.toIso8601String(),
      'break_start_time': breakStartTime?.toIso8601String(),
      'break_end_time': breakEndTime?.toIso8601String(),
      'total_hours': totalHours,
      'regular_hours': regularHours,
      'overtime_hours': overtimeHours,
      'late_minutes': lateMinutes,
      'early_leave_minutes': earlyLeaveMinutes,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// التحقق من وجود تسجيل دخول
  bool get hasCheckIn => checkInTime != null;

  /// التحقق من وجود تسجيل خروج
  bool get hasCheckOut => checkOutTime != null;

  /// التحقق من اكتمال اليوم
  bool get isComplete => hasCheckIn && hasCheckOut;

  /// حساب ساعات العمل الفعلية
  double get actualWorkingHours {
    if (!isComplete) return 0;

    final workDuration = checkOutTime!.difference(checkInTime!);
    double hours = workDuration.inMinutes / 60.0;

    // خصم فترة الاستراحة إذا كانت موجودة
    if (breakStartTime != null && breakEndTime != null) {
      final breakDuration = breakEndTime!.difference(breakStartTime!);
      hours -= breakDuration.inMinutes / 60.0;
    }

    return hours > 0 ? hours : 0;
  }

  /// إنشاء نسخة معدلة من الحضور
  Attendance copyWith({
    int? id,
    int? employeeId,
    DateTime? attendanceDate,
    DateTime? checkInTime,
    DateTime? checkOutTime,
    DateTime? breakStartTime,
    DateTime? breakEndTime,
    double? totalHours,
    double? regularHours,
    double? overtimeHours,
    int? lateMinutes,
    int? earlyLeaveMinutes,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Attendance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      attendanceDate: attendanceDate ?? this.attendanceDate,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      breakStartTime: breakStartTime ?? this.breakStartTime,
      breakEndTime: breakEndTime ?? this.breakEndTime,
      totalHours: totalHours ?? this.totalHours,
      regularHours: regularHours ?? this.regularHours,
      overtimeHours: overtimeHours ?? this.overtimeHours,
      lateMinutes: lateMinutes ?? this.lateMinutes,
      earlyLeaveMinutes: earlyLeaveMinutes ?? this.earlyLeaveMinutes,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
