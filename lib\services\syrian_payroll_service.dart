/// خدمة حساب الرواتب والضرائب السورية
/// تحسب الضرائب والضمان الاجتماعي وفق القوانين السورية
library;

import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/journal_entry_service.dart';
import '../services/account_service.dart';
import '../models/journal_entry.dart';

/// نموذج حساب الراتب
class SalaryCalculation {
  final int employeeId;
  final double basicSalary;
  final double allowances;
  final double overtimeAmount;
  final double bonusAmount;
  final double deductions;
  final double loanDeduction;
  final double taxAmount;
  final double insuranceAmount;
  final double netSalary;
  final double grossSalary;
  final int workingDays;
  final int actualWorkingDays;
  final double overtimeHours;
  final double lateHours;
  final int absenceDays;

  const SalaryCalculation({
    required this.employeeId,
    required this.basicSalary,
    required this.allowances,
    required this.overtimeAmount,
    required this.bonusAmount,
    required this.deductions,
    required this.loanDeduction,
    required this.taxAmount,
    required this.insuranceAmount,
    required this.netSalary,
    required this.grossSalary,
    required this.workingDays,
    required this.actualWorkingDays,
    required this.overtimeHours,
    required this.lateHours,
    required this.absenceDays,
  });

  Map<String, dynamic> toMap() {
    return {
      'employee_id': employeeId,
      'basic_salary': basicSalary,
      'allowances': allowances,
      'overtime_amount': overtimeAmount,
      'bonus_amount': bonusAmount,
      'deductions': deductions,
      'loan_deduction': loanDeduction,
      'tax_amount': taxAmount,
      'insurance_amount': insuranceAmount,
      'net_salary': netSalary,
      'gross_salary': grossSalary,
      'working_days': workingDays,
      'actual_working_days': actualWorkingDays,
      'overtime_hours': overtimeHours,
      'late_hours': lateHours,
      'absence_days': absenceDays,
    };
  }
}

class SyrianPayrollService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final JournalEntryService _journalEntryService = JournalEntryService();
  final AccountService _accountService = AccountService();

  /// حساب راتب موظف لشهر معين
  Future<SalaryCalculation> calculateEmployeeSalary({
    required int employeeId,
    required int month,
    required int year,
    double additionalAllowances = 0,
    double additionalDeductions = 0,
    double bonusAmount = 0,
  }) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على بيانات الموظف
      final employeeResult = await db.query(
        AppConstants.employeesTable,
        where: 'id = ?',
        whereArgs: [employeeId],
        limit: 1,
      );

      if (employeeResult.isEmpty) {
        throw Exception('الموظف غير موجود');
      }

      final employee = Employee.fromMap(employeeResult.first);

      // الحصول على بيانات الحضور للشهر
      final attendanceData = await _getMonthlyAttendanceData(
        employeeId,
        month,
        year,
      );

      // حساب الراتب الأساسي المتناسب مع أيام العمل الفعلية
      final workingDays = _getWorkingDaysInMonth(month, year);
      final actualWorkingDays = attendanceData['actualWorkingDays'] as int;
      final overtimeHours = attendanceData['overtimeHours'] as double;
      final lateHours = attendanceData['lateHours'] as double;
      final absenceDays = workingDays - actualWorkingDays;

      // حساب الراتب الأساسي المتناسب
      final proportionalBasicSalary =
          (employee.basicSalary / workingDays) * actualWorkingDays;

      // حساب بدل الساعات الإضافية (1.5 ضعف الساعة العادية)
      final hourlyRate =
          employee.basicSalary / (workingDays * 8); // 8 ساعات عمل يومياً
      final overtimeAmount = overtimeHours * hourlyRate * 1.5;

      // حساب البدلات الإجمالية
      final totalAllowances = additionalAllowances;

      // حساب الخصومات من التأخير (خصم ساعة من كل 3 ساعات تأخير)
      final lateDeduction = (lateHours / 3) * hourlyRate;

      // الحصول على خصم القروض للشهر
      final loanDeduction = await _getMonthlyLoanDeduction(
        employeeId,
        month,
        year,
      );

      // حساب إجمالي الخصومات
      final totalDeductions =
          additionalDeductions + lateDeduction + loanDeduction;

      // حساب الراتب الإجمالي قبل الضرائب والضمان
      final grossSalary =
          proportionalBasicSalary +
          totalAllowances +
          overtimeAmount +
          bonusAmount -
          totalDeductions;

      // حساب ضريبة الدخل السورية
      final taxAmount = _calculateSyrianIncomeTax(grossSalary);

      // حساب اشتراك الضمان الاجتماعي
      final insuranceAmount = _calculateSyrianSocialInsurance(grossSalary);

      // حساب الراتب الصافي
      final netSalary = grossSalary - taxAmount - insuranceAmount;

      return SalaryCalculation(
        employeeId: employeeId,
        basicSalary: proportionalBasicSalary,
        allowances: totalAllowances,
        overtimeAmount: overtimeAmount,
        bonusAmount: bonusAmount,
        deductions: totalDeductions,
        loanDeduction: loanDeduction,
        taxAmount: taxAmount,
        insuranceAmount: insuranceAmount,
        netSalary: netSalary,
        grossSalary: grossSalary,
        workingDays: workingDays,
        actualWorkingDays: actualWorkingDays,
        overtimeHours: overtimeHours,
        lateHours: lateHours,
        absenceDays: absenceDays,
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب راتب الموظف',
        category: 'SyrianPayrollService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// حساب ضريبة الدخل السورية وفق الشرائح
  double _calculateSyrianIncomeTax(double grossSalary) {
    double tax = 0;
    double remainingSalary = grossSalary;

    // الشريحة الأولى: حتى 2 مليون ليرة - 5%
    if (remainingSalary > 0) {
      final taxableInBracket1 =
          remainingSalary > AppConstants.syrianTaxBracket1Limit
          ? AppConstants.syrianTaxBracket1Limit
          : remainingSalary;
      tax += taxableInBracket1 * AppConstants.syrianTaxBracket1;
      remainingSalary -= taxableInBracket1;
    }

    // الشريحة الثانية: من 2 إلى 4 مليون ليرة - 10%
    if (remainingSalary > 0) {
      final bracketLimit =
          AppConstants.syrianTaxBracket2Limit -
          AppConstants.syrianTaxBracket1Limit;
      final taxableInBracket2 = remainingSalary > bracketLimit
          ? bracketLimit
          : remainingSalary;
      tax += taxableInBracket2 * AppConstants.syrianTaxBracket2;
      remainingSalary -= taxableInBracket2;
    }

    // الشريحة الثالثة: من 4 إلى 6 مليون ليرة - 15%
    if (remainingSalary > 0) {
      final bracketLimit =
          AppConstants.syrianTaxBracket3Limit -
          AppConstants.syrianTaxBracket2Limit;
      final taxableInBracket3 = remainingSalary > bracketLimit
          ? bracketLimit
          : remainingSalary;
      tax += taxableInBracket3 * AppConstants.syrianTaxBracket3;
      remainingSalary -= taxableInBracket3;
    }

    // الشريحة الرابعة: أكثر من 6 مليون ليرة - 20%
    if (remainingSalary > 0) {
      tax += remainingSalary * AppConstants.syrianTaxBracket4;
    }

    return tax;
  }

  /// حساب اشتراك الضمان الاجتماعي السوري
  double _calculateSyrianSocialInsurance(double grossSalary) {
    // تطبيق الحد الأقصى للراتب الخاضع للضمان
    final taxableSalary =
        grossSalary > AppConstants.syrianSocialInsuranceMaxSalary
        ? AppConstants.syrianSocialInsuranceMaxSalary
        : grossSalary;

    // حساب اشتراك الموظف (7%)
    return taxableSalary * AppConstants.syrianSocialInsuranceEmployeeRate;
  }

  /// حساب اشتراك صاحب العمل في الضمان الاجتماعي
  double calculateEmployerSocialInsurance(double grossSalary) {
    final taxableSalary =
        grossSalary > AppConstants.syrianSocialInsuranceMaxSalary
        ? AppConstants.syrianSocialInsuranceMaxSalary
        : grossSalary;

    return taxableSalary * AppConstants.syrianSocialInsuranceEmployerRate;
  }

  /// الحصول على بيانات الحضور الشهرية
  Future<Map<String, dynamic>> _getMonthlyAttendanceData(
    int employeeId,
    int month,
    int year,
  ) async {
    final db = await _databaseHelper.database;

    final startDate = DateTime(year, month, 1);
    final endDate = DateTime(year, month + 1, 0); // آخر يوم في الشهر

    final result = await db.rawQuery(
      '''
      SELECT 
        COUNT(*) as total_days,
        COUNT(CASE WHEN status = 'present' THEN 1 END) as present_days,
        COALESCE(SUM(overtime_hours), 0) as total_overtime_hours,
        COALESCE(SUM(late_minutes), 0) as total_late_minutes
      FROM ${AppConstants.attendanceTable}
      WHERE employee_id = ? 
        AND attendance_date BETWEEN ? AND ?
    ''',
      [
        employeeId,
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ],
    );

    final data = result.first;
    return {
      'actualWorkingDays': data['present_days'] as int,
      'overtimeHours': (data['total_overtime_hours'] as num).toDouble(),
      'lateHours':
          ((data['total_late_minutes'] as num).toDouble()) /
          60, // تحويل الدقائق إلى ساعات
    };
  }

  /// حساب عدد أيام العمل في الشهر (باستثناء الجمعة والسبت)
  int _getWorkingDaysInMonth(int month, int year) {
    int workingDays = 0;
    final daysInMonth = DateTime(year, month + 1, 0).day;

    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(year, month, day);
      // في سوريا، عطلة نهاية الأسبوع هي الجمعة والسبت
      if (date.weekday != DateTime.friday &&
          date.weekday != DateTime.saturday) {
        workingDays++;
      }
    }

    return workingDays;
  }

  /// الحصول على خصم القروض الشهري
  Future<double> _getMonthlyLoanDeduction(
    int employeeId,
    int month,
    int year,
  ) async {
    final db = await _databaseHelper.database;

    final result = await db.rawQuery(
      '''
      SELECT COALESCE(SUM(li.amount), 0) as total_deduction
      FROM ${AppConstants.loanInstallmentsTable} li
      JOIN ${AppConstants.loansTable} l ON li.loan_id = l.id
      WHERE l.employee_id = ? 
        AND li.status = 'pending'
        AND strftime('%Y-%m', li.due_date) = ?
    ''',
      [employeeId, '$year-${month.toString().padLeft(2, '0')}'],
    );

    return (result.first['total_deduction'] as num).toDouble();
  }

  /// توليد قيد محاسبي للرواتب
  Future<void> generatePayrollJournalEntry({
    required int payrollId,
    required List<SalaryCalculation> salaryCalculations,
    required DateTime payDate,
  }) async {
    try {
      // حساب الإجماليات
      double totalBasicSalary = 0;
      double totalAllowances = 0;
      double totalDeductions = 0;
      double totalTax = 0;
      double totalInsurance = 0;
      double totalNetSalary = 0;
      double totalEmployerInsurance = 0;

      for (final calc in salaryCalculations) {
        totalBasicSalary += calc.basicSalary;
        totalAllowances += calc.allowances;
        totalDeductions += calc.deductions;
        totalTax += calc.taxAmount;
        totalInsurance += calc.insuranceAmount;
        totalNetSalary += calc.netSalary;
        totalEmployerInsurance += calculateEmployerSocialInsurance(
          calc.grossSalary,
        );
      }

      // الحصول على الحسابات المحاسبية
      final salaryExpenseAccount = await _accountService.getAccountByCode(
        '6100',
      ); // مصروف الرواتب
      final taxPayableAccount = await _accountService.getAccountByCode(
        '2100',
      ); // ضرائب مستحقة الدفع
      final insurancePayableAccount = await _accountService.getAccountByCode(
        '2200',
      ); // ضمان اجتماعي مستحق الدفع
      final salaryPayableAccount = await _accountService.getAccountByCode(
        '2300',
      ); // رواتب مستحقة الدفع

      if (salaryExpenseAccount == null ||
          taxPayableAccount == null ||
          insurancePayableAccount == null ||
          salaryPayableAccount == null) {
        throw Exception('الحسابات المحاسبية المطلوبة للرواتب غير موجودة');
      }

      // إنشاء القيد المحاسبي
      final entryNumber = await _journalEntryService.generateEntryNumber();

      final journalEntryDetails = [
        // مدين: مصروف الرواتب (الراتب الأساسي + البدلات + حصة صاحب العمل من الضمان)
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: salaryExpenseAccount.id!,
          debitAmount:
              totalBasicSalary + totalAllowances + totalEmployerInsurance,
          creditAmount: 0.0,
          description: 'مصروف رواتب الشهر',
        ),
        // دائن: ضرائب مستحقة الدفع
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: taxPayableAccount.id!,
          debitAmount: 0.0,
          creditAmount: totalTax,
          description: 'ضرائب مقتطعة من الرواتب',
        ),
        // دائن: ضمان اجتماعي مستحق الدفع (حصة الموظف + حصة صاحب العمل)
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: insurancePayableAccount.id!,
          debitAmount: 0.0,
          creditAmount: totalInsurance + totalEmployerInsurance,
          description: 'اشتراكات الضمان الاجتماعي',
        ),
        // دائن: رواتب مستحقة الدفع (الراتب الصافي)
        JournalEntryDetail(
          journalEntryId: 0, // سيتم تحديثه لاحقاً
          accountId: salaryPayableAccount.id!,
          debitAmount: 0.0,
          creditAmount: totalNetSalary,
          description: 'رواتب صافية مستحقة الدفع',
        ),
      ];

      final journalEntry = JournalEntry(
        entryNumber: entryNumber,
        entryDate: payDate,
        description: 'قيد رواتب الشهر ${payDate.month}/${payDate.year}',
        reference: 'PAYROLL-$payrollId',
        type: 'payroll',
        totalDebit: totalBasicSalary + totalAllowances + totalEmployerInsurance,
        totalCredit:
            totalTax + totalInsurance + totalEmployerInsurance + totalNetSalary,
        isPosted: true,
        details: journalEntryDetails,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _journalEntryService.insertJournalEntry(journalEntry);

      LoggingService.info(
        'تم إنشاء قيد الرواتب بنجاح',
        category: 'SyrianPayrollService',
        data: {
          'payrollId': payrollId,
          'totalNetSalary': totalNetSalary,
          'employeeCount': salaryCalculations.length,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قيد الرواتب',
        category: 'SyrianPayrollService',
        data: {'payrollId': payrollId, 'error': e.toString()},
      );
      rethrow;
    }
  }
}
