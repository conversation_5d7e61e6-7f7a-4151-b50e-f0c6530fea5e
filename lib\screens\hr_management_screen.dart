/// شاشة إدارة الموارد البشرية
/// الشاشة الرئيسية لنظام HR مع تبويبات للموظفين والرواتب والحضور
library;

import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import 'add_employee_screen.dart';
import 'attendance_screen.dart';
import 'payroll_screen.dart';
import 'loans_screen.dart';
import 'hr_reports_screen.dart';
import 'hr_dashboard_screen.dart';
import 'advanced_contracts_screen.dart';

class HRManagementScreen extends StatefulWidget {
  const HRManagementScreen({super.key});

  @override
  State<HRManagementScreen> createState() => _HRManagementScreenState();
}

class _HRManagementScreenState extends State<HRManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final EmployeeService _employeeService = EmployeeService();

  List<Employee> _employees = [];
  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
    _loadEmployees();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadEmployees() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final employees = await _employeeService.getAllEmployees(
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      setState(() {
        _employees = employees;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل قائمة الموظفين',
        category: 'HRManagementScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الموارد البشرية'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'لوحة التحكم'),
            Tab(icon: Icon(Icons.people), text: 'الموظفين'),
            Tab(icon: Icon(Icons.account_balance_wallet), text: 'الرواتب'),
            Tab(icon: Icon(Icons.access_time), text: 'الحضور والانصراف'),
            Tab(icon: Icon(Icons.money_off), text: 'السلف والقروض'),
            Tab(icon: Icon(Icons.description), text: 'العقود'),
            Tab(icon: Icon(Icons.assessment), text: 'التقارير'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDashboardTab(),
          _buildEmployeesTab(),
          _buildSalariesTab(),
          _buildAttendanceTab(),
          _buildLoansTab(),
          _buildContractsTab(),
          _buildReportsTab(),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildEmployeesTab() {
    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            decoration: InputDecoration(
              hintText: 'البحث عن موظف...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Colors.grey[100],
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _loadEmployees();
            },
          ),
        ),
        // قائمة الموظفين
        Expanded(child: _buildEmployeesList()),
      ],
    );
  }

  Widget _buildEmployeesList() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل قائمة الموظفين...');
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadEmployees,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_employees.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty
                  ? 'لا توجد نتائج للبحث'
                  : 'لا يوجد موظفين مسجلين',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              _searchQuery.isNotEmpty
                  ? 'جرب البحث بكلمات مختلفة'
                  : 'اضغط على زر + لإضافة موظف جديد',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _employees.length,
      itemBuilder: (context, index) {
        final employee = _employees[index];
        return _buildEmployeeCard(employee);
      },
    );
  }

  Widget _buildEmployeeCard(Employee employee) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showEmployeeDetails(employee),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // صورة الموظف أو أيقونة افتراضية
              CircleAvatar(
                radius: 30,
                backgroundColor: RevolutionaryColors.damascusSky.withValues(
                  alpha: 0.1,
                ),
                backgroundImage: employee.photoPath != null
                    ? AssetImage(employee.photoPath!)
                    : null,
                child: employee.photoPath == null
                    ? Icon(
                        Icons.person,
                        size: 30,
                        color: RevolutionaryColors.damascusSky,
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              // معلومات الموظف
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      employee.displayName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'رقم الموظف: ${employee.employeeNumber}',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.phone, size: 16, color: Colors.grey[500]),
                        const SizedBox(width: 4),
                        Text(
                          employee.phone ?? 'غير محدد',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // حالة الموظف
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(
                    employee.status,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getStatusColor(employee.status),
                    width: 1,
                  ),
                ),
                child: Text(
                  _getStatusText(employee.status),
                  style: TextStyle(
                    fontSize: 12,
                    color: _getStatusColor(employee.status),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // قائمة الخيارات
              PopupMenuButton<String>(
                onSelected: (value) => _handleEmployeeAction(value, employee),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view',
                    child: Row(
                      children: [
                        Icon(Icons.visibility, size: 20),
                        SizedBox(width: 8),
                        Text('عرض التفاصيل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'salary',
                    child: Row(
                      children: [
                        Icon(Icons.account_balance_wallet, size: 20),
                        SizedBox(width: 8),
                        Text('حساب الراتب'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSalariesTab() {
    return const PayrollScreen();
  }

  Widget _buildAttendanceTab() {
    return const AttendanceScreen();
  }

  Widget _buildLoansTab() {
    return const LoansScreen();
  }

  Widget _buildContractsTab() {
    return const AdvancedContractsScreen();
  }

  Widget _buildDashboardTab() {
    return const HRDashboardScreen();
  }

  Widget _buildReportsTab() {
    return const HRReportsScreen();
  }

  Widget? _buildFloatingActionButton() {
    // إظهار زر الإضافة فقط في تبويب الموظفين (التبويب الثاني الآن)
    if (_tabController.index == 1) {
      return FloatingActionButton.extended(
        onPressed: _addNewEmployee,
        backgroundColor: RevolutionaryColors.successGlow,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text('إضافة موظف', style: TextStyle(color: Colors.white)),
      );
    }
    return null;
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.employeeStatusActive:
        return RevolutionaryColors.successGlow;
      case AppConstants.employeeStatusInactive:
        return RevolutionaryColors.warningAmber;
      case AppConstants.employeeStatusTerminated:
        return RevolutionaryColors.errorCoral;
      case AppConstants.employeeStatusSuspended:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case AppConstants.employeeStatusActive:
        return 'نشط';
      case AppConstants.employeeStatusInactive:
        return 'غير نشط';
      case AppConstants.employeeStatusTerminated:
        return 'منتهي الخدمة';
      case AppConstants.employeeStatusSuspended:
        return 'موقوف';
      default:
        return 'غير محدد';
    }
  }

  void _showEmployeeDetails(Employee employee) {
    // TODO: تنفيذ شاشة تفاصيل الموظف
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض تفاصيل الموظف: ${employee.displayName}'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }

  void _handleEmployeeAction(String action, Employee employee) {
    switch (action) {
      case 'view':
        _showEmployeeDetails(employee);
        break;
      case 'edit':
        // TODO: تنفيذ شاشة تعديل الموظف
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تعديل الموظف: ${employee.displayName}'),
            backgroundColor: RevolutionaryColors.warningAmber,
          ),
        );
        break;
      case 'salary':
        // TODO: تنفيذ شاشة حساب الراتب
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حساب راتب: ${employee.displayName}'),
            backgroundColor: RevolutionaryColors.damascusSky,
          ),
        );
        break;
      case 'delete':
        _confirmDeleteEmployee(employee);
        break;
    }
  }

  void _addNewEmployee() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddEmployeeScreen()),
    );

    // إذا تم إضافة موظف بنجاح، أعد تحميل القائمة
    if (result == true) {
      _loadEmployees();
    }
  }

  void _confirmDeleteEmployee(Employee employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموظف "${employee.displayName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteEmployee(employee);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteEmployee(Employee employee) async {
    try {
      await _employeeService.deleteEmployee(employee.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف الموظف "${employee.displayName}" بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }

      _loadEmployees();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الموظف: ${e.toString()}'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }
}
