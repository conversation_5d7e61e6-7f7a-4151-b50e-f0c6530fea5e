/// شاشة إدارة القروض والسلف
/// واجهة لإدارة قروض الموظفين والأقساط
library;

import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/loan_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';

class LoansScreen extends StatefulWidget {
  const LoansScreen({super.key});

  @override
  State<LoansScreen> createState() => _LoansScreenState();
}

class _LoansScreenState extends State<LoansScreen> {
  final LoanService _loanService = LoanService();
  final EmployeeService _employeeService = EmployeeService();

  List<Employee> _employees = [];
  List<Loan> _loans = [];
  List<LoanInstallment> _overdueInstallments = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تحميل قائمة الموظفين النشطين
      final employees = await _employeeService.getAllEmployees(
        activeOnly: true,
      );

      // تحميل جميع القروض
      final allLoans = <Loan>[];
      for (final employee in employees) {
        final employeeLoans = await _loanService.getEmployeeLoans(employee.id!);
        allLoans.addAll(employeeLoans);
      }

      // تحميل الأقساط المتأخرة
      final overdueInstallments = await _loanService.getOverdueInstallments();

      setState(() {
        _employees = employees;
        _loans = allLoans;
        _overdueInstallments = overdueInstallments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات القروض',
        category: 'LoansScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة القروض والسلف'),
        backgroundColor: RevolutionaryColors.warningAmber,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
        ],
      ),
      body: Column(
        children: [
          _buildSummaryCards(),
          Expanded(child: _buildLoansContent()),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddLoanDialog,
        backgroundColor: RevolutionaryColors.warningAmber,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text('قرض جديد', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final activeLoans = _loans.where((loan) => loan.isActive).length;
    final totalAmount = _loans.fold<double>(
      0,
      (sum, loan) => sum + loan.totalAmount,
    );
    final totalPaid = _loans.fold<double>(
      0,
      (sum, loan) => sum + loan.paidAmount,
    );
    final totalRemaining = _loans.fold<double>(
      0,
      (sum, loan) => sum + loan.remainingAmount,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'القروض النشطة',
              activeLoans.toString(),
              Icons.account_balance_wallet,
              RevolutionaryColors.warningAmber,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'إجمالي المبلغ',
              '${totalAmount.toStringAsFixed(0)} ل.س',
              Icons.monetization_on,
              RevolutionaryColors.damascusSky,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'المدفوع',
              '${totalPaid.toStringAsFixed(0)} ل.س',
              Icons.check_circle,
              RevolutionaryColors.successGlow,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'المتبقي',
              '${totalRemaining.toStringAsFixed(0)} ل.س',
              Icons.pending,
              RevolutionaryColors.errorCoral,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoansContent() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل بيانات القروض...');
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_loans.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد قروض',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'لم يتم إنشاء أي قروض بعد',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          const TabBar(
            tabs: [
              Tab(text: 'جميع القروض'),
              Tab(text: 'الأقساط المتأخرة'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [_buildLoansTab(), _buildOverdueInstallmentsTab()],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoansTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _loans.length,
      itemBuilder: (context, index) {
        final loan = _loans[index];
        final employee = _employees.firstWhere(
          (emp) => emp.id == loan.employeeId,
          orElse: () => Employee(
            employeeNumber: 'غير معروف',
            nationalId: '',
            firstName: 'موظف',
            lastName: 'محذوف',
            fullName: 'موظف محذوف',
            hireDate: DateTime.now(),
            basicSalary: 0,
            status: 'inactive',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        return _buildLoanCard(loan, employee);
      },
    );
  }

  Widget _buildOverdueInstallmentsTab() {
    if (_overdueInstallments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle_outline, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text(
              'لا توجد أقساط متأخرة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('جميع الأقساط محدثة', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _overdueInstallments.length,
      itemBuilder: (context, index) {
        final installment = _overdueInstallments[index];
        return _buildOverdueInstallmentCard(installment);
      },
    );
  }

  Widget _buildLoanCard(Loan loan, Employee employee) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: RevolutionaryColors.warningAmber.withValues(
                    alpha: 0.1,
                  ),
                  child: Icon(
                    Icons.person,
                    color: RevolutionaryColors.warningAmber,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        employee.displayName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'رقم الموظف: ${employee.employeeNumber}',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                _buildLoanStatus(loan),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            _buildLoanDetails(loan),
            const SizedBox(height: 12),
            _buildLoanActions(loan),
          ],
        ),
      ),
    );
  }

  Widget _buildLoanStatus(Loan loan) {
    Color statusColor;
    String statusText;

    if (loan.isCompleted) {
      statusColor = RevolutionaryColors.successGlow;
      statusText = 'مكتمل';
    } else if (loan.isActive) {
      statusColor = RevolutionaryColors.warningAmber;
      statusText = 'نشط';
    } else {
      statusColor = RevolutionaryColors.errorCoral;
      statusText = 'ملغي';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          fontSize: 12,
          color: statusColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildLoanDetails(Loan loan) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildDetailItem(
                'مبلغ القرض',
                '${loan.amount.toStringAsFixed(0)} ل.س',
                RevolutionaryColors.damascusSky,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDetailItem(
                'عدد الأقساط',
                '${loan.installments} قسط',
                RevolutionaryColors.infoTurquoise,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDetailItem(
                'المدفوع',
                '${loan.paidAmount.toStringAsFixed(0)} ل.س',
                RevolutionaryColors.successGlow,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDetailItem(
                'المتبقي',
                '${loan.remainingAmount.toStringAsFixed(0)} ل.س',
                RevolutionaryColors.errorCoral,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: loan.paymentProgress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            RevolutionaryColors.successGlow,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'نسبة السداد: ${(loan.paymentProgress * 100).toStringAsFixed(1)}%',
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildLoanActions(Loan loan) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _showLoanDetails(loan),
            icon: const Icon(Icons.visibility, size: 18),
            label: const Text('التفاصيل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.damascusSky,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
        const SizedBox(width: 8),
        if (loan.isActive)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _showInstallments(loan),
              icon: const Icon(Icons.payment, size: 18),
              label: const Text('الأقساط'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.warningAmber,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildOverdueInstallmentCard(LoanInstallment installment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning,
                  color: RevolutionaryColors.errorCoral,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'قسط متأخر - رقم ${installment.installmentNumber}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: RevolutionaryColors.errorCoral.withValues(
                      alpha: 0.1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: RevolutionaryColors.errorCoral),
                  ),
                  child: Text(
                    'متأخر',
                    style: TextStyle(
                      fontSize: 12,
                      color: RevolutionaryColors.errorCoral,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    'المبلغ',
                    '${installment.amount.toStringAsFixed(0)} ل.س',
                    RevolutionaryColors.warningAmber,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDetailItem(
                    'تاريخ الاستحقاق',
                    '${installment.dueDate.day}/${installment.dueDate.month}/${installment.dueDate.year}',
                    RevolutionaryColors.errorCoral,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: () => _payInstallment(installment),
              icon: const Icon(Icons.payment, size: 18),
              label: const Text('دفع القسط'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.successGlow,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 40),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddLoanDialog() {
    // TODO: تنفيذ شاشة إضافة قرض جديد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('إضافة قرض جديد - قريباً'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }

  void _showLoanDetails(Loan loan) {
    // TODO: تنفيذ شاشة تفاصيل القرض
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تفاصيل القرض - قريباً'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }

  void _showInstallments(Loan loan) {
    // TODO: تنفيذ شاشة أقساط القرض
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('أقساط القرض - قريباً'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }

  Future<void> _payInstallment(LoanInstallment installment) async {
    try {
      await _loanService.payInstallment(installmentId: installment.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم دفع القسط بنجاح'),
            backgroundColor: RevolutionaryColors.successGlow,
          ),
        );
      }

      _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في دفع القسط: ${e.toString()}'),
            backgroundColor: RevolutionaryColors.errorCoral,
          ),
        );
      }
    }
  }
}
